{"rustc": 12488743700189009532, "features": "[\"accessibility\", \"backend-winit\", \"backend-winit-wayland\", \"backend-winit-x11\", \"default\", \"i-slint-backend-qt\", \"i-slint-backend-winit\", \"renderer-femtovg\", \"renderer-software\"]", "declared_features": "[\"accessibility\", \"backend-linuxkms\", \"backend-linuxkms-noseat\", \"backend-qt\", \"backend-winit\", \"backend-winit-wayland\", \"backend-winit-x11\", \"default\", \"i-slint-backend-linuxkms\", \"i-slint-backend-qt\", \"i-slint-backend-testing\", \"i-slint-backend-winit\", \"i-slint-renderer-skia\", \"raw-window-handle-06\", \"renderer-femtovg\", \"renderer-skia\", \"renderer-skia-opengl\", \"renderer-skia-vulkan\", \"renderer-software\", \"rtti\", \"system-testing\"]", "target": 17512649618174676683, "profile": 2241668132362809309, "path": 1219209512976785871, "deps": [[2134775856264564058, "i_slint_backend_qt", false, 739251619021033260], [5756138707274668043, "i_slint_core_macros", false, 16552359434057782374], [7613193051436156431, "i_slint_core", false, 3926653424973604320], [7649432603155880922, "build_script_build", false, 14050752054741862522], [10411997081178400487, "cfg_if", false, 15669488323948584752], [17699788962609858224, "i_slint_backend_winit", false, 12068428276085349067]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\i-slint-backend-selector-44c57cea856e7f33\\dep-lib-i_slint_backend_selector", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}